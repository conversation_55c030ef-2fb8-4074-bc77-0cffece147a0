import { BaseContextProvider } from "../";
import {
  ContextItem,
  ContextProviderDescription,
  ContextProviderExtras,
} from "../../";

interface KnowledgeDocument {
  id: string;
  fileName: string;
  fileSize: number;
  categoryName?: string;
  createTime: string;
  status: string;
}

interface KnowledgeDocumentDetail extends KnowledgeDocument {
  content: string;
  createUser: string;
}

interface KnowledgeSearchResult {
  id: string;
  contentId: string;
  contentType: string;
  title: string;
  content: string;
  score: number;
  categoryId: string;
  categoryName: string;
  tags: string[];
  sourceFileId: string;
  sourceFileName: string;
  createTime: string;
  updateTime: string;
  similarity: number;
}

interface KnowledgeApiResponse<T> {
  code: number;
  requestId: string;
  data: T;
  errors: string[];
  serviceSuccess: boolean;
  redirectUrl?: string;
}

class KnowledgeContextProvider extends BaseContextProvider {
  static description: ContextProviderDescription = {
    title: "knowledge",
    displayTitle: "知识库",
    description: "从知识库中检索相关文档",
    type: "normal",
  };

  private baseUrl = "http://**************:8081/lowcodeback";

  private async getOrgId(extras: ContextProviderExtras): Promise<string> {
    // 优先使用options中配置的orgId
    if (this.options.orgId) {
      return this.options.orgId;
    }

    // 使用固定的orgId
    return "********************************";
  }

  private async listDocuments(
    orgId: string,
    extras: ContextProviderExtras,
  ): Promise<KnowledgeDocument[]> {
    const response = await extras.fetch(
      `${this.baseUrl}/api/knowledge/listDocuments`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: new URLSearchParams({
          appId: orgId,
        }).toString(),
      },
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: KnowledgeApiResponse<KnowledgeDocument[]> = await response.json();

    if (data.serviceSuccess && data.code === 200) {
      return data.data || [];
    } else {
      throw new Error(data.errors?.join(", ") || "获取文档列表失败");
    }
  }

  private async viewDocument(
    documentId: string,
    orgId: string,
    extras: ContextProviderExtras,
  ): Promise<KnowledgeDocumentDetail> {
    const response = await extras.fetch(
      `${this.baseUrl}/api/knowledge/viewDocument`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: new URLSearchParams({
          appId: orgId,
          documentId: documentId,
        }).toString(),
      },
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: KnowledgeApiResponse<KnowledgeDocumentDetail> = await response.json();

    if (data.serviceSuccess && data.code === 200) {
      return data.data;
    } else {
      throw new Error(data.errors?.join(", ") || "获取文档内容失败");
    }
  }

  private async selectRelevantDocuments(
    documents: KnowledgeDocument[],
    query: string,
    extras: ContextProviderExtras,
  ): Promise<KnowledgeDocument[]> {
    if (documents.length === 0) {
      return [];
    }

    // 构建文档列表的描述
    const documentList = documents.map((doc, index) =>
      `${index + 1}. 文件名: ${doc.fileName}, 分类: ${doc.categoryName || '无'}, 创建时间: ${doc.createTime}`
    ).join('\n');

    const prompt = `请根据用户查询选择最相关的文档。

用户查询: ${query}

可选文档列表:
${documentList}

请从上述文档中选择最多5个与用户查询最相关的文档，返回它们的序号（用逗号分隔）。
如果没有相关文档，请返回"无"。

示例回答格式: 1,3,5 或 无`;

    try {
      // 使用当前选择的模型进行文档选择
      const llm = await extras.llm.complete(prompt);
      const response = llm.trim();

      if (response === "无" || response.toLowerCase() === "none") {
        return [];
      }

      // 解析选择的文档序号
      const selectedIndices = response
        .split(',')
        .map(s => parseInt(s.trim()) - 1)
        .filter(i => i >= 0 && i < documents.length);

      return selectedIndices.map(i => documents[i]);
    } catch (error) {
      console.error("Error selecting relevant documents:", error);
      // 如果模型选择失败，返回前5个文档作为备选
      return documents.slice(0, 5);
    }
  }

  private async extractRelevantContent(
    document: KnowledgeDocumentDetail,
    query: string,
    extras: ContextProviderExtras,
  ): Promise<string> {
    const prompt = `请从以下文档内容中提取与用户查询相关的知识片段。

用户查询: ${query}

文档内容:
${document.content}

请提取与查询最相关的内容片段，保持原文的准确性，并确保提取的内容完整且有意义。
如果文档中没有相关内容，请返回"该文档中未找到相关内容"。`;

    try {
      const llm = await extras.llm.complete(prompt);
      return llm.trim();
    } catch (error) {
      console.error("Error extracting relevant content:", error);
      // 如果提取失败，返回文档的前1000个字符
      return document.content.substring(0, 1000) + (document.content.length > 1000 ? "..." : "");
    }
  }

  async getContextItems(
    query: string,
    extras: ContextProviderExtras,
  ): Promise<ContextItem[]> {
    try {
      const orgId = await this.getOrgId(extras);

      // 对于normal类型的provider，实际的查询内容在extras.fullInput中
      const searchQuery = extras.fullInput.split("知识库")[1] || query;
      console.log("Knowledge provider debug:", {
        query,
        fullInput: extras.fullInput,
        searchQuery,
      });

      // 如果没有查询内容，返回提示信息
      if (!searchQuery || searchQuery.trim() === "") {
        return [
          {
            name: "知识库",
            description: "请在@知识库后面输入您要搜索的内容",
            content: "使用方式：@知识库 您的问题或关键词",
          },
        ];
      }

      // 1. 获取知识库文档列表
      const documents = await this.listDocuments(orgId, extras);

      if (documents.length === 0) {
        return [
          {
            icon: "problems",
            name: "无文档",
            description: "知识库中没有文档",
            content: "知识库中暂无文档，请先上传相关文档。",
          },
        ];
      }

      // 2. 使用longtext模型选择相关文档
      const relevantDocuments = await this.selectRelevantDocuments(
        documents,
        searchQuery.trim(),
        extras,
      );

      if (relevantDocuments.length === 0) {
        return [
          {
            icon: "problems",
            name: "无相关文档",
            description: "未找到相关的知识库文档",
            content: `没有找到与"${searchQuery.trim()}"相关的知识库文档，请尝试其他关键词。`,
          },
        ];
      }

      // 3. 获取相关文档的详细内容并提取相关片段
      const contextItems: ContextItem[] = [];

      for (const doc of relevantDocuments) {
        try {
          const documentDetail = await this.viewDocument(doc.id, orgId, extras);
          const relevantContent = await this.extractRelevantContent(
            documentDetail,
            searchQuery.trim(),
            extras,
          );

          if (relevantContent !== "该文档中未找到相关内容") {
            contextItems.push({
              icon: "docs",
              name: doc.fileName,
              description: `知识库文档 - ${doc.categoryName || '无分类'}`,
              content: relevantContent,
            });
          }
        } catch (error) {
          console.error(`Error processing document ${doc.id}:`, error);
          // 继续处理其他文档
        }
      }

      if (contextItems.length === 0) {
        return [
          {
            icon: "problems",
            name: "无相关内容",
            description: "文档中未找到相关内容",
            content: `在相关文档中未找到与"${searchQuery.trim()}"相关的具体内容。`,
          },
        ];
      }

      return contextItems;
    } catch (error) {
      console.error("Knowledge context provider error:", error);
      return [
        {
          icon: "problems",
          name: "错误",
          description: "知识库查询失败",
          content: `错误: ${error instanceof Error ? error.message : "未知错误"}`,
        },
      ];
    }
  }
}

export default KnowledgeContextProvider;
